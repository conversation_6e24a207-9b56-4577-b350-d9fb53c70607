{"name": "arien-ai-cli", "version": "1.0.0", "description": "Sophisticated AI-powered CLI terminal system with LLM integration and autonomous tool execution", "main": "dist/index.js", "bin": {"arien": "dist/index.js"}, "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "tsc", "build:unix": "tsc && chmod +x dist/index.js", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "typecheck": "tsc --noEmit", "install:global": "npm run build && npm install -g .", "uninstall:global": "npm uninstall -g arien-ai-cli"}, "keywords": ["ai", "cli", "terminal", "llm", "deepseek", "ollama", "typescript", "assistant"], "author": "Arien AI Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "boxen": "^7.1.1", "chalk": "^5.3.0", "cli-cursor": "^4.0.0", "cli-spinners": "^2.9.2", "commander": "^11.1.0", "conf": "^11.0.2", "figlet": "^1.7.0", "glob": "^10.3.10", "inquirer": "^9.2.12", "keypress": "^0.2.1", "node-fetch": "^3.3.2", "ora": "^7.0.1", "strip-ansi": "^7.1.0", "ws": "^8.14.2"}, "devDependencies": {"@types/figlet": "^1.5.8", "@types/inquirer": "^9.0.8", "@types/node": "^20.10.5", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitest/coverage-v8": "^1.0.4", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "tsx": "^4.6.2", "typescript": "^5.3.3", "vitest": "^1.0.4"}}