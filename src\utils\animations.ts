import { LoadingAnimation, AnimationFrame } from '../types/index.js';

export const LOADING_ANIMATIONS: Record<string, LoadingAnimation> = {
  ball: {
    name: 'ball',
    interval: 200,
    frames: [
      { frame: '( ●    )', duration: 200 },
      { frame: '(  ●   )', duration: 200 },
      { frame: '(   ●  )', duration: 200 },
      { frame: '(    ● )', duration: 200 },
      { frame: '(     ●)', duration: 200 },
      { frame: '(    ● )', duration: 200 },
      { frame: '(   ●  )', duration: 200 },
      { frame: '(  ●   )', duration: 200 },
      { frame: '( ●    )', duration: 200 },
      { frame: '(●     )', duration: 200 },
    ],
  },
  dots: {
    name: 'dots',
    interval: 300,
    frames: [
      { frame: '⠋', duration: 300 },
      { frame: '⠙', duration: 300 },
      { frame: '⠹', duration: 300 },
      { frame: '⠸', duration: 300 },
      { frame: '⠼', duration: 300 },
      { frame: '⠴', duration: 300 },
      { frame: '⠦', duration: 300 },
      { frame: '⠧', duration: 300 },
      { frame: '⠇', duration: 300 },
      { frame: '⠏', duration: 300 },
    ],
  },
  thinking: {
    name: 'thinking',
    interval: 500,
    frames: [
      { frame: '🤔', duration: 500 },
      { frame: '💭', duration: 500 },
      { frame: '🧠', duration: 500 },
      { frame: '💡', duration: 500 },
    ],
  },
  processing: {
    name: 'processing',
    interval: 150,
    frames: [
      { frame: '[    ]', duration: 150 },
      { frame: '[=   ]', duration: 150 },
      { frame: '[==  ]', duration: 150 },
      { frame: '[=== ]', duration: 150 },
      { frame: '[====]', duration: 150 },
      { frame: '[ ===]', duration: 150 },
      { frame: '[  ==]', duration: 150 },
      { frame: '[   =]', duration: 150 },
    ],
  },
};

export class AnimationManager {
  private currentAnimation: NodeJS.Timeout | null = null;
  private frameIndex = 0;
  private startTime = 0;

  start(
    animationName: keyof typeof LOADING_ANIMATIONS,
    callback: (frame: string, elapsed: number) => void
  ): void {
    this.stop();

    const animation = LOADING_ANIMATIONS[animationName];
    if (!animation) {
      throw new Error(`Animation "${animationName}" not found`);
    }

    this.frameIndex = 0;
    this.startTime = Date.now();

    const animate = (): void => {
      const frame = animation.frames[this.frameIndex];
      const elapsed = Math.floor((Date.now() - this.startTime) / 1000);

      callback(frame.frame, elapsed);

      this.frameIndex = (this.frameIndex + 1) % animation.frames.length;
      this.currentAnimation = setTimeout(animate, frame.duration);
    };

    animate();
  }

  stop(): void {
    if (this.currentAnimation) {
      clearTimeout(this.currentAnimation);
      this.currentAnimation = null;
    }
  }

  isRunning(): boolean {
    return this.currentAnimation !== null;
  }
}

export function createProgressBar(
  current: number,
  total: number,
  width = 20,
  filled = '█',
  empty = '░'
): string {
  const percentage = Math.min(current / total, 1);
  const filledWidth = Math.floor(percentage * width);
  const emptyWidth = width - filledWidth;

  const bar = filled.repeat(filledWidth) + empty.repeat(emptyWidth);
  const percent = Math.floor(percentage * 100);

  return `[${bar}] ${percent}%`;
}

export function formatElapsedTime(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes < 60) {
    return `${minutes}m ${remainingSeconds}s`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
}

export function createSpinner(text: string, animationName: keyof typeof LOADING_ANIMATIONS = 'dots'): {
  start: () => void;
  stop: () => void;
  update: (newText: string) => void;
} {
  const animation = new AnimationManager();
  let currentText = text;

  return {
    start: () => {
      process.stdout.write('\x1B[?25l'); // Hide cursor
      animation.start(animationName, (frame, elapsed) => {
        const elapsedStr = formatElapsedTime(elapsed);
        process.stdout.write(`\r${frame} ${currentText} (${elapsedStr})`);
      });
    },

    stop: () => {
      animation.stop();
      process.stdout.write('\r\x1B[K'); // Clear line
      process.stdout.write('\x1B[?25h'); // Show cursor
    },

    update: (newText: string) => {
      currentText = newText;
    },
  };
}
