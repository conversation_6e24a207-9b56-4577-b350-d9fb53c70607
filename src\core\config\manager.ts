import Conf from 'conf';
import { CLIConfig, StoredConfig, ProviderConfig, PromptTemplate } from '../../types/index.js';
import { createError, ErrorCodes } from '../../utils/errors.js';

export class ConfigManager {
  private store: Conf<StoredConfig>;
  private defaultConfig: CLIConfig;

  constructor() {
    this.store = new Conf<StoredConfig>({
      projectName: 'arien-ai-cli',
      schema: {
        providers: {
          type: 'object',
          default: {},
        },
        currentProvider: {
          type: 'string',
          default: '',
        },
        userPreferences: {
          type: 'object',
          properties: {
            theme: { type: 'string', default: 'auto' },
            autoSave: { type: 'boolean', default: true },
            notifications: { type: 'boolean', default: true },
            verboseLogging: { type: 'boolean', default: false },
          },
          default: {
            theme: 'auto',
            autoSave: true,
            notifications: true,
            verboseLogging: false,
          },
        },
        promptTemplates: {
          type: 'array',
          default: [],
        },
        lastUpdated: {
          type: 'string',
          default: new Date().toISOString(),
        },
      },
    });

    this.defaultConfig = {
      provider: '',
      model: '',
      temperature: 0.7,
      maxTokens: 4000,
      systemPrompt: this.getDefaultSystemPrompt(),
      autoSave: true,
      theme: 'auto',
    };
  }

  // Provider Management
  async saveProvider(name: string, config: ProviderConfig): Promise<void> {
    try {
      const providers = this.store.get('providers', {});
      providers[name] = config;
      this.store.set('providers', providers);
      this.store.set('lastUpdated', new Date().toISOString());
    } catch (error) {
      throw createError(
        `Failed to save provider configuration: ${(error as Error).message}`,
        'CONFIG_SAVE_FAILED',
        'config',
        false,
        { provider: name }
      );
    }
  }

  getProvider(name: string): ProviderConfig | null {
    const providers = this.store.get('providers', {});
    return providers[name] || null;
  }

  getAllProviders(): Record<string, ProviderConfig> {
    return this.store.get('providers', {});
  }

  deleteProvider(name: string): void {
    const providers = this.store.get('providers', {});
    delete providers[name];
    this.store.set('providers', providers);

    // If this was the current provider, clear it
    if (this.store.get('currentProvider') === name) {
      this.store.set('currentProvider', '');
    }
  }

  // Current Provider Management
  setCurrentProvider(name: string): void {
    const providers = this.store.get('providers', {});
    if (!providers[name]) {
      throw createError(
        `Provider "${name}" not found`,
        'CONFIG_INVALID',
        'config',
        false,
        { provider: name }
      );
    }
    this.store.set('currentProvider', name);
  }

  getCurrentProvider(): string {
    return this.store.get('currentProvider', '');
  }

  getCurrentProviderConfig(): ProviderConfig | null {
    const currentProvider = this.getCurrentProvider();
    return currentProvider ? this.getProvider(currentProvider) : null;
  }

  // CLI Configuration
  getCLIConfig(): CLIConfig {
    const currentProviderConfig = this.getCurrentProviderConfig();
    const preferences = this.store.get('userPreferences');

    return {
      ...this.defaultConfig,
      provider: this.getCurrentProvider(),
      model: currentProviderConfig?.model || '',
      apiKey: currentProviderConfig?.apiKey,
      baseUrl: currentProviderConfig?.baseUrl,
      temperature: currentProviderConfig?.temperature || this.defaultConfig.temperature,
      maxTokens: currentProviderConfig?.maxTokens || this.defaultConfig.maxTokens,
      autoSave: preferences.autoSave,
      theme: preferences.theme as 'light' | 'dark' | 'auto',
    };
  }

  updateCLIConfig(updates: Partial<CLIConfig>): void {
    const currentProvider = this.getCurrentProvider();
    if (currentProvider && updates.model) {
      const providerConfig = this.getProvider(currentProvider);
      if (providerConfig) {
        providerConfig.model = updates.model;
        this.saveProvider(currentProvider, providerConfig);
      }
    }

    if (updates.theme || updates.autoSave !== undefined) {
      const preferences = this.store.get('userPreferences');
      this.store.set('userPreferences', {
        ...preferences,
        ...(updates.theme && { theme: updates.theme }),
        ...(updates.autoSave !== undefined && { autoSave: updates.autoSave }),
      });
    }
  }

  // Validation
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const currentProvider = this.getCurrentProvider();

    if (!currentProvider) {
      errors.push('No provider configured');
    } else {
      const providerConfig = this.getProvider(currentProvider);
      if (!providerConfig) {
        errors.push(`Provider "${currentProvider}" configuration not found`);
      } else {
        if (!providerConfig.model) {
          errors.push('No model specified');
        }
        // Add more validation as needed
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Prompt Templates
  savePromptTemplate(template: PromptTemplate): void {
    const templates = this.store.get('promptTemplates', []);
    const existingIndex = templates.findIndex((t: PromptTemplate) => t.id === template.id);

    if (existingIndex >= 0) {
      templates[existingIndex] = template;
    } else {
      templates.push(template);
    }

    this.store.set('promptTemplates', templates);
  }

  getPromptTemplates(): PromptTemplate[] {
    return this.store.get('promptTemplates', []);
  }

  getPromptTemplate(id: string): PromptTemplate | null {
    const templates = this.getPromptTemplates();
    return templates.find(t => t.id === id) || null;
  }

  // Utility Methods
  reset(): void {
    this.store.clear();
  }

  export(): StoredConfig {
    return this.store.store;
  }

  import(config: Partial<StoredConfig>): void {
    Object.entries(config).forEach(([key, value]) => {
      this.store.set(key as keyof StoredConfig, value);
    });
  }

  private getDefaultSystemPrompt(): string {
    return `You are Arien AI, a sophisticated AI assistant with access to various tools and capabilities. 

Your core responsibilities:
1. Analyze user requests intelligently and determine the best approach
2. Use available tools autonomously when they would help accomplish the user's goals
3. Provide clear, helpful responses with proper reasoning
4. Handle errors gracefully and suggest alternatives when needed
5. Maintain context across conversations and learn from interactions

Available capabilities include:
- File system operations (read, write, search)
- Web browsing and information retrieval
- Code execution and development tools
- System command execution
- Real-time information access

Always prioritize user safety and ask for confirmation before performing potentially destructive operations.
Be concise but thorough in your responses, and explain your reasoning when using tools.`;
  }
}
