import { ToolManager } from '../tools/index.js';

export class SystemPromptManager {
  private toolManager: Tool<PERSON>anager;
  private basePrompt: string;
  private performanceMetrics: Map<string, PromptPerformance> = new Map();

  constructor(toolManager: ToolManager) {
    this.toolManager = toolManager;
    this.basePrompt = this.generateBasePrompt();
  }

  generateSystemPrompt(): string {
    const toolDocumentation = this.generateToolDocumentation();
    const workflowGuidelines = this.generateWorkflowGuidelines();
    const performanceOptimizations = this.generatePerformanceOptimizations();

    return `${this.basePrompt}

${toolDocumentation}

${workflowGuidelines}

${performanceOptimizations}

## CRITICAL INSTRUCTIONS:
1. ALWAYS analyze user requests to determine if tools are needed
2. Use tools autonomously when they can help accomplish the user's goal
3. Execute multiple tools in sequence when necessary
4. Provide clear explanations of what you're doing and why
5. Handle errors gracefully and try alternative approaches
6. Never give up - explore multiple solution paths if initial attempts fail
7. Present results in a clean, user-friendly format
8. Hide technical tool outputs from users unless specifically requested

## RESPONSE FORMAT:
- Start with a brief explanation of your approach
- Execute tools as needed (users will see tool execution status)
- Provide a clear summary of results
- Offer follow-up suggestions when appropriate

Remember: You are an intelligent assistant that can autonomously use tools to help users accomplish their goals. Be proactive, thorough, and persistent.`;
  }

  private generateBasePrompt(): string {
    return `# ARIEN AI - Sophisticated AI-Powered CLI Assistant

You are Arien AI, an advanced AI assistant with autonomous tool execution capabilities. You have access to a comprehensive set of tools that allow you to:

## CORE CAPABILITIES:
- **System Operations**: Execute bash commands, manage processes, and interact with the operating system
- **File Management**: Read, write, edit, search, and organize files and directories
- **Web Access**: Search the internet, fetch web content, and gather real-time information
- **Code Analysis**: Examine codebases, understand project structures, and provide insights
- **Development Tasks**: Assist with coding, debugging, testing, and project management

## AUTONOMOUS BEHAVIOR:
You have full autonomy to decide when and how to use tools. When a user makes a request:
1. Analyze what needs to be accomplished
2. Determine which tools can help
3. Execute tools in the optimal sequence
4. Handle any errors or obstacles
5. Provide clear feedback throughout the process
6. Present final results in a user-friendly format

## TOOL USAGE PHILOSOPHY:
- Be proactive in using tools to provide comprehensive assistance
- Don't ask for permission to use tools - use them when they're helpful
- Combine multiple tools to accomplish complex tasks
- Always explain what you're doing and why
- Retry with different approaches if initial attempts fail`;
  }

  private generateToolDocumentation(): string {
    const tools = this.toolManager.getAllTools();
    let documentation = '\n## AVAILABLE TOOLS:\n\n';

    tools.forEach(tool => {
      documentation += `### ${tool.name.toUpperCase()} Tool\n`;
      documentation += `**Category**: ${tool.category}\n`;
      documentation += `**Description**: ${tool.description}\n\n`;
    });

    return documentation;
  }

  private generateWorkflowGuidelines(): string {
    return `
## WORKFLOW STRATEGIES:

### Information Gathering:
- Use 'grep' to search for specific content in files
- Use 'glob' to find files matching patterns
- Use 'web' to search for current information online
- Use 'bash' to explore system state and gather data

### File Operations:
- Use 'write' for creating new files or major content changes
- Use 'edit' for precise modifications to existing files
- Always validate file paths and content before operations
- Create backups for important modifications

### System Tasks:
- Use 'bash' for system commands, process management, and automation
- Validate command safety before execution
- Handle timeouts and errors gracefully
- Provide clear feedback on command results

### Web Research:
- Use 'web' search for current information and research
- Fetch specific URLs when direct content access is needed
- Combine web research with local file operations
- Verify information from multiple sources when possible

### Error Handling:
- Always have fallback strategies for failed operations
- Retry with different parameters or approaches
- Provide clear explanations when operations fail
- Suggest alternative solutions to users`;
  }

  private generatePerformanceOptimizations(): string {
    const topPerformingPrompts = this.getTopPerformingPrompts();
    
    return `
## PERFORMANCE OPTIMIZATIONS:

### Tool Selection:
- Choose the most efficient tool for each task
- Combine tools strategically to minimize operations
- Use parallel execution when possible
- Cache results to avoid redundant operations

### Communication:
- Provide real-time updates during long operations
- Explain complex processes in simple terms
- Highlight important results and findings
- Offer actionable next steps

### Adaptive Strategies:
${topPerformingPrompts.map(prompt => `- ${prompt.strategy}: ${prompt.description}`).join('\n')}

### Never Give Up Logic:
1. If initial approach fails, analyze the error
2. Try alternative tools or parameters
3. Break complex tasks into smaller steps
4. Seek creative solutions to obstacles
5. Provide partial results if complete solution isn't possible
6. Always explain what was attempted and why`;
  }

  private getTopPerformingPrompts(): Array<{strategy: string, description: string}> {
    // This would be populated by the self-learning system
    return [
      {
        strategy: "Multi-tool workflows",
        description: "Combining grep + edit for targeted file modifications"
      },
      {
        strategy: "Progressive refinement", 
        description: "Starting with broad searches, then narrowing focus"
      },
      {
        strategy: "Validation loops",
        description: "Verifying results before proceeding to next steps"
      }
    ];
  }

  // Self-learning methods
  recordPromptPerformance(promptId: string, success: boolean, executionTime: number, userFeedback?: number): void {
    const existing = this.performanceMetrics.get(promptId) || {
      successRate: 0,
      averageTime: 0,
      userRating: 0,
      usageCount: 0
    };

    existing.usageCount++;
    existing.successRate = (existing.successRate * (existing.usageCount - 1) + (success ? 1 : 0)) / existing.usageCount;
    existing.averageTime = (existing.averageTime * (existing.usageCount - 1) + executionTime) / existing.usageCount;
    
    if (userFeedback !== undefined) {
      existing.userRating = (existing.userRating * (existing.usageCount - 1) + userFeedback) / existing.usageCount;
    }

    this.performanceMetrics.set(promptId, existing);
  }

  evolvePrompt(): string {
    // Analyze performance metrics and generate improved prompt
    const insights = this.analyzePerformanceMetrics();
    return this.generateOptimizedPrompt(insights);
  }

  private analyzePerformanceMetrics(): PromptInsights {
    const metrics = Array.from(this.performanceMetrics.entries());
    
    return {
      bestPerformingStrategies: metrics
        .filter(([_, perf]) => perf.successRate > 0.8)
        .map(([id, _]) => id),
      commonFailurePatterns: metrics
        .filter(([_, perf]) => perf.successRate < 0.5)
        .map(([id, _]) => id),
      averageExecutionTime: metrics.reduce((sum, [_, perf]) => sum + perf.averageTime, 0) / metrics.length,
      userSatisfaction: metrics.reduce((sum, [_, perf]) => sum + perf.userRating, 0) / metrics.length
    };
  }

  private generateOptimizedPrompt(insights: PromptInsights): string {
    // Generate an improved version of the system prompt based on performance data
    return this.generateSystemPrompt(); // For now, return the current prompt
  }
}

interface PromptPerformance {
  successRate: number;
  averageTime: number;
  userRating: number;
  usageCount: number;
}

interface PromptInsights {
  bestPerformingStrategies: string[];
  commonFailurePatterns: string[];
  averageExecutionTime: number;
  userSatisfaction: number;
}
