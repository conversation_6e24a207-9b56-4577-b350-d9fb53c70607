#!/bin/bash

# Arien AI CLI Universal Installer
# Supports Windows 11 WSL, macOS, and Linux

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/arien-ai/cli.git"
INSTALL_DIR="$HOME/.arien-ai"
BIN_NAME="arien"
NODE_MIN_VERSION="22.0.0"

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if grep -q Microsoft /proc/version 2>/dev/null; then
            OS="wsl"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
}

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ARIEN AI CLI INSTALLER                    ║"
    echo "║              Sophisticated AI-powered CLI System            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Compare version numbers
version_compare() {
    if [[ $1 == $2 ]]; then
        return 0
    fi
    local IFS=.
    local i ver1=($1) ver2=($2)
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++)); do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++)); do
        if [[ -z ${ver2[i]} ]]; then
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]})); then
            return 1
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]})); then
            return 2
        fi
    done
    return 0
}

# Check Node.js version
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed"
        print_status "Please install Node.js ${NODE_MIN_VERSION} or later from https://nodejs.org/"
        return 1
    fi
    
    local node_version=$(node --version | sed 's/v//')
    print_status "Found Node.js version: $node_version"
    
    version_compare $node_version $NODE_MIN_VERSION
    case $? in
        0|1) print_success "Node.js version is compatible" ;;
        2) 
            print_error "Node.js version $node_version is too old. Required: $NODE_MIN_VERSION or later"
            return 1
            ;;
    esac
    
    if ! command_exists npm; then
        print_error "npm is not installed"
        return 1
    fi
    
    print_success "Node.js and npm are properly installed"
    return 0
}

# Install dependencies based on OS
install_dependencies() {
    print_status "Installing system dependencies..."
    
    case $OS in
        "linux"|"wsl")
            if command_exists apt-get; then
                sudo apt-get update
                sudo apt-get install -y git curl build-essential
            elif command_exists yum; then
                sudo yum install -y git curl gcc-c++ make
            elif command_exists pacman; then
                sudo pacman -S --noconfirm git curl base-devel
            else
                print_warning "Unknown package manager. Please install git and curl manually."
            fi
            ;;
        "macos")
            if ! command_exists git; then
                print_status "Installing Xcode command line tools..."
                xcode-select --install
            fi
            if command_exists brew; then
                brew install git curl
            fi
            ;;
        "windows")
            print_warning "Please ensure git and curl are installed on Windows"
            ;;
    esac
}

# Clone or update repository
setup_repository() {
    print_status "Setting up Arien AI CLI..."
    
    if [[ -d "$INSTALL_DIR" ]]; then
        print_status "Updating existing installation..."
        cd "$INSTALL_DIR"
        git pull origin main
    else
        print_status "Cloning repository..."
        git clone "$REPO_URL" "$INSTALL_DIR"
        cd "$INSTALL_DIR"
    fi
}

# Install npm dependencies and build
build_project() {
    print_status "Installing dependencies and building project..."
    cd "$INSTALL_DIR"
    
    npm install
    npm run build
    
    print_success "Project built successfully"
}

# Create global symlink
install_globally() {
    print_status "Installing globally..."
    cd "$INSTALL_DIR"
    
    # Create bin directory if it doesn't exist
    mkdir -p "$HOME/.local/bin"
    
    # Create symlink
    local bin_path="$HOME/.local/bin/$BIN_NAME"
    if [[ -L "$bin_path" ]] || [[ -f "$bin_path" ]]; then
        rm -f "$bin_path"
    fi
    
    ln -s "$INSTALL_DIR/dist/index.js" "$bin_path"
    chmod +x "$INSTALL_DIR/dist/index.js"
    
    print_success "Arien AI CLI installed to $bin_path"
}

# Update PATH in shell configuration
update_path() {
    local shell_config=""
    local bin_dir="$HOME/.local/bin"
    
    # Detect shell and config file
    if [[ -n "$ZSH_VERSION" ]] || [[ "$SHELL" == *"zsh"* ]]; then
        shell_config="$HOME/.zshrc"
    elif [[ -n "$BASH_VERSION" ]] || [[ "$SHELL" == *"bash"* ]]; then
        if [[ "$OS" == "macos" ]]; then
            shell_config="$HOME/.bash_profile"
        else
            shell_config="$HOME/.bashrc"
        fi
    elif [[ "$SHELL" == *"fish"* ]]; then
        shell_config="$HOME/.config/fish/config.fish"
    fi
    
    if [[ -n "$shell_config" ]]; then
        # Check if PATH already includes the bin directory
        if ! grep -q "$bin_dir" "$shell_config" 2>/dev/null; then
            print_status "Adding $bin_dir to PATH in $shell_config"
            echo "" >> "$shell_config"
            echo "# Added by Arien AI CLI installer" >> "$shell_config"
            echo "export PATH=\"$bin_dir:\$PATH\"" >> "$shell_config"
            print_success "PATH updated in $shell_config"
        else
            print_status "PATH already includes $bin_dir"
        fi
    else
        print_warning "Could not detect shell configuration file"
        print_warning "Please manually add $bin_dir to your PATH"
    fi
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Source the shell config to update PATH for this session
    export PATH="$HOME/.local/bin:$PATH"
    
    if command_exists "$BIN_NAME"; then
        local version=$($BIN_NAME --version 2>/dev/null || echo "unknown")
        print_success "Arien AI CLI installed successfully! Version: $version"
        print_status "Run '$BIN_NAME setup' to configure your AI providers"
        return 0
    else
        print_error "Installation verification failed"
        print_warning "You may need to restart your terminal or run: source ~/.bashrc (or ~/.zshrc)"
        return 1
    fi
}

# Uninstall function
uninstall() {
    print_status "Uninstalling Arien AI CLI..."
    
    # Remove installation directory
    if [[ -d "$INSTALL_DIR" ]]; then
        rm -rf "$INSTALL_DIR"
        print_success "Removed installation directory"
    fi
    
    # Remove symlink
    local bin_path="$HOME/.local/bin/$BIN_NAME"
    if [[ -L "$bin_path" ]] || [[ -f "$bin_path" ]]; then
        rm -f "$bin_path"
        print_success "Removed binary symlink"
    fi
    
    print_success "Arien AI CLI uninstalled successfully"
    print_warning "You may want to manually remove PATH entries from your shell configuration"
}

# Update function
update() {
    print_status "Updating Arien AI CLI..."
    
    if [[ ! -d "$INSTALL_DIR" ]]; then
        print_error "Arien AI CLI is not installed"
        exit 1
    fi
    
    setup_repository
    build_project
    verify_installation
}

# Main installation function
install() {
    print_header
    print_status "Starting installation for $OS..."
    
    check_nodejs || exit 1
    install_dependencies
    setup_repository
    build_project
    install_globally
    update_path
    verify_installation
    
    echo ""
    print_success "Installation completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "  1. Restart your terminal or run: source ~/.bashrc"
    echo "  2. Run: $BIN_NAME setup"
    echo "  3. Start chatting: $BIN_NAME"
    echo ""
}

# Show usage
show_usage() {
    echo "Usage: $0 [install|update|uninstall]"
    echo ""
    echo "Commands:"
    echo "  install    Install Arien AI CLI (default)"
    echo "  update     Update existing installation"
    echo "  uninstall  Remove Arien AI CLI"
    echo ""
    echo "Examples:"
    echo "  $0                # Install"
    echo "  $0 install        # Install"
    echo "  $0 update         # Update"
    echo "  $0 uninstall      # Uninstall"
}

# Main script
main() {
    detect_os
    
    case "${1:-install}" in
        "install")
            install
            ;;
        "update")
            update
            ;;
        "uninstall")
            uninstall
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
