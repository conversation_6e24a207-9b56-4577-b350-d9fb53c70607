import { exec } from 'child_process';
import { promisify } from 'util';
import { access, constants } from 'fs/promises';
import { Tool, FunctionResult } from '../../types/index.js';

const execAsync = promisify(exec);

export class GrepTool implements Tool {
  public readonly name = 'grep';
  public readonly description = 'Fast content search tool that finds files containing specific text or patterns, returning matching file paths sorted by modification time (newest first)';
  public readonly category = 'file' as const;

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { pattern, path, recursive = true, caseInsensitive = false, maxResults = 50 } = args;

      if (typeof pattern !== 'string' || typeof path !== 'string') {
        return {
          success: false,
          error: 'Pattern and path must be strings',
        };
      }

      // Check if path exists
      try {
        await access(path, constants.F_OK);
      } catch {
        return {
          success: false,
          error: `Path does not exist: ${path}`,
        };
      }

      const grepOptions = this.buildGrepOptions(
        typeof recursive === 'boolean' ? recursive : true,
        typeof caseInsensitive === 'boolean' ? caseInsensitive : false
      );
      const command = `grep ${grepOptions} "${this.escapePattern(pattern)}" "${path}" 2>/dev/null | head -${maxResults}`;

      const { stdout, stderr } = await execAsync(command, {
        timeout: 15000,
        maxBuffer: 1024 * 1024,
      });

      const matches = this.parseGrepOutput(stdout);

      return {
        success: true,
        result: {
          pattern,
          path,
          matches,
          totalMatches: matches.length,
          searchOptions: {
            recursive,
            caseInsensitive,
            maxResults,
          },
        },
        output: matches.length > 0 
          ? `Found ${matches.length} matches for "${pattern}"`
          : `No matches found for "${pattern}"`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Search failed: ${error.message}`,
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    return (
      typeof args.pattern === 'string' &&
      typeof args.path === 'string' &&
      args.pattern.trim().length > 0 &&
      args.path.trim().length > 0
    );
  }

  private buildGrepOptions(recursive: boolean, caseInsensitive: boolean): string {
    let options = '-n'; // Show line numbers

    if (recursive) {
      options += 'r'; // Recursive search
    }

    if (caseInsensitive) {
      options += 'i'; // Case insensitive
    }

    options += 'H'; // Always show filename
    options += ' --color=never'; // No color output

    return options;
  }

  private escapePattern(pattern: string): string {
    // Escape special characters for grep
    return pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private parseGrepOutput(output: string): Array<{
    file: string;
    line: number;
    content: string;
    match: string;
  }> {
    if (!output.trim()) {
      return [];
    }

    const lines = output.trim().split('\n');
    const matches: Array<{
      file: string;
      line: number;
      content: string;
      match: string;
    }> = [];

    for (const line of lines) {
      const match = line.match(/^([^:]+):(\d+):(.*)$/);
      if (match) {
        const [, file, lineNum, content] = match;
        matches.push({
          file: file.trim(),
          line: parseInt(lineNum, 10),
          content: content.trim(),
          match: line,
        });
      }
    }

    // Sort by file modification time (would need additional stat calls for true sorting)
    // For now, just return in the order grep found them
    return matches;
  }

  getDocumentation(): string {
    return `
# GREP Tool

**Category:** File
**Description:** Fast content search tool that finds files containing specific text or patterns

## Primary Use Cases:
- Search for specific text across multiple files
- Find code patterns and functions
- Locate configuration values
- Debug by finding error messages or logs
- Code analysis and refactoring assistance

## When NOT to Use:
- For binary file searches (use specialized tools)
- When you need complex regex that grep doesn't support well
- For very large codebases where performance is critical (consider ripgrep)
- When you need semantic code search (use language-specific tools)

## Parameters:
- **pattern** (required): Text pattern or regex to search for
- **path** (required): File or directory path to search in
- **recursive** (optional): Search recursively in directories (default: true)
- **caseInsensitive** (optional): Perform case-insensitive search (default: false)
- **maxResults** (optional): Maximum number of results to return (default: 50)

## Best Practices:
- Use specific patterns to reduce noise in results
- Start with smaller directories for complex patterns
- Use case-insensitive search for user input
- Escape special regex characters when searching for literal text
- Consider performance impact on large directories

## Performance Considerations:
- 15-second timeout for searches
- Results limited to prevent memory issues
- Recursive searches can be slow on large directories
- Binary files are automatically skipped

## Integration Notes:
- Works well with edit tool for making changes to found files
- Combine with glob tool for file-type-specific searches
- Output format is consistent for easy parsing

## Examples:
\`\`\`json
{"pattern": "function", "path": "./src", "recursive": true}
{"pattern": "TODO", "path": ".", "caseInsensitive": true}
{"pattern": "import.*react", "path": "./components"}
\`\`\`

## Common Patterns:
- Function definitions: "function\\s+\\w+"
- Import statements: "import.*from"
- TODO comments: "TODO|FIXME|HACK"
- Error handling: "try\\s*{|catch\\s*\\("
`;
  }
}
